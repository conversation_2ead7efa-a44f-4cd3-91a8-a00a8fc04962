<?php
/**
 * 个人形象分析中转API
 * 接收用户数据，通过Gemini API进行分析，返回分析结果
 * 此API部署在 https://www.furrywoo.com/gemini/gerenxx.php
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => 'Method Not Allowed']);
    exit;
}

// 设置Gemini API密钥和模型
$apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg'; // 替换为实际API密钥
$model = 'gemini-2.5-flash-lite-preview-06-17';

// 构建Gemini API URL
$url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$apiKey}";

// 获取POST数据
$postData = file_get_contents('php://input');
$requestData = json_decode($postData, true);

// 记录请求数据到日志文件
$logFile = __DIR__ . '/logs/personal_image_analysis.log';
$logDir = dirname($logFile);
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}
file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "收到请求: " . json_encode($requestData, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);

// 检查必要参数
if (!isset($requestData['userData']) || empty($requestData['userData'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少用户数据']);
    exit;
}

// 提取用户数据
$userData = $requestData['userData'];
$photoUrls = isset($requestData['photoUrls']) ? $requestData['photoUrls'] : [];
$photoBase64 = isset($requestData['photoBase64']) ? $requestData['photoBase64'] : [];
$analysisId = isset($requestData['analysisId']) ? $requestData['analysisId'] : null;

// 构建提示词
$prompt = buildPrompt($userData, $photoUrls);

// 记录提示词到日志
file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "生成的提示词: " . $prompt . PHP_EOL, FILE_APPEND);

// 构建请求数据
$geminiRequestData = [
    'contents' => [
        [
            'parts' => [
                [
                    'text' => $prompt
                ]
            ]
        ]
    ],
    'generationConfig' => [
        'temperature' => 0.4,
        'topP' => 0.8,
        'topK' => 40,
        'maxOutputTokens' => 8192
    ]
];

// 优先使用base64编码的图片
if (!empty($photoBase64)) {
    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "使用base64编码的图片，数量: " . count($photoBase64) . PHP_EOL, FILE_APPEND);
    foreach ($photoBase64 as $index => $base64Image) {
        try {
            // 检测MIME类型
            $mimeType = detectImageMimeTypeFromBase64($base64Image);
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "检测到图片 #" . ($index + 1) . " 的MIME类型: " . $mimeType . PHP_EOL, FILE_APPEND);

            // 添加到请求中
            $geminiRequestData['contents'][0]['parts'][] = [
                'inline_data' => [
                    'mime_type' => $mimeType,
                    'data' => $base64Image
                ]
            ];

            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "成功添加base64图片 #" . ($index + 1) . " (MIME: " . $mimeType . ")" . PHP_EOL, FILE_APPEND);
        } catch (Exception $e) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "处理base64图片时出错: " . $e->getMessage() . PHP_EOL, FILE_APPEND);
        }
    }
}
// 如果没有base64图片，尝试使用URL（保留原有逻辑作为备用）
else if (!empty($photoUrls)) {
    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "没有base64图片，尝试使用URL，数量: " . count($photoUrls) . PHP_EOL, FILE_APPEND);
    foreach ($photoUrls as $index => $photoUrl) {
        try {
            // 使用增强的下载方法获取图片内容
            $imageContent = downloadImageWithRetry($photoUrl, $logFile);
            if ($imageContent === false) {
                file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "无法获取图片内容: " . $photoUrl . PHP_EOL, FILE_APPEND);
                continue;
            }

            // 确定MIME类型
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mimeType = $finfo->buffer($imageContent);

            // 如果finfo无法确定类型，使用文件头检测
            if (!$mimeType || $mimeType === 'application/octet-stream') {
                $base64Image = base64_encode($imageContent);
                $mimeType = detectImageMimeTypeFromBase64($base64Image);
                file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "使用文件头检测到MIME类型: " . $mimeType . PHP_EOL, FILE_APPEND);
            } else {
                // 转换为base64
                $base64Image = base64_encode($imageContent);
            }

            // 添加到请求中
            $geminiRequestData['contents'][0]['parts'][] = [
                'inline_data' => [
                    'mime_type' => $mimeType,
                    'data' => $base64Image
                ]
            ];

            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "成功添加图片 #" . ($index + 1) . " (MIME: " . $mimeType . ")" . PHP_EOL, FILE_APPEND);
        } catch (Exception $e) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "处理图片时出错: " . $e->getMessage() . PHP_EOL, FILE_APPEND);
        }
    }
}

// 发送请求到Gemini API
$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($geminiRequestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

// 记录API响应
file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "Gemini API响应 (HTTP $httpCode): " . $response . PHP_EOL, FILE_APPEND);

// 检查错误
if ($curlError) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => 'API请求失败: ' . $curlError]);
    exit;
}

if ($httpCode !== 200) {
    http_response_code($httpCode);
    echo $response;
    exit;
}

// 解析响应
$geminiResponse = json_decode($response, true);

// 提取生成的文本
$generatedText = '';
if (isset($geminiResponse['candidates'][0]['content']['parts'][0]['text'])) {
    $generatedText = $geminiResponse['candidates'][0]['content']['parts'][0]['text'];
}

// 尝试将生成的文本解析为JSON
$analysisResult = extractJsonFromText($generatedText);

// 记录解析结果
file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "解析结果: " . json_encode($analysisResult, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);

// 返回最终结果
echo json_encode([
    'error' => false,
    'data' => $analysisResult,
    'rawText' => $generatedText,
    'analysisId' => $analysisId
]);

/**
 * 构建提示词
 * 
 * @param array $userData 用户数据
 * @param array $photoUrls 照片URL数组
 * @return string 提示词
 */
function buildPrompt($userData, $photoUrls) {
    $photoCount = count($photoUrls);
    
    $prompt = "你是一位超专业、时尚且充满热情的形象分析师和个人风格顾问，擅长通过人们的照片和身材数据提供专业的形象分析和穿搭建议。你的风格类似小红书博主，语言亲切活泼，充满鼓励性，善于发现用户的优点并给予真诚的赞美，但不要太浮夸。现在有一位用户提供了以下信息，请你进行全面的形象分析。\n\n";
    
    // 添加用户身材数据
    $prompt .= "用户身材数据：\n";
    if (isset($userData['gender'])) {
        $prompt .= "- 性别：" . ($userData['gender'] == 1 ? '男' : '女') . "\n";
    }
    if (isset($userData['height'])) {
        $prompt .= "- 身高：" . $userData['height'] . " cm\n";
    }
    if (isset($userData['weight'])) {
        $prompt .= "- 体重：" . $userData['weight'] . " kg\n";
    }
    if (isset($userData['bust'])) {
        $prompt .= "- 胸围：" . $userData['bust'] . " cm\n";
    }
    if (isset($userData['waist'])) {
        $prompt .= "- 腰围：" . $userData['waist'] . " cm\n";
    }
    if (isset($userData['hips'])) {
        $prompt .= "- 臀围：" . $userData['hips'] . " cm\n";
    }
    if (isset($userData['shoulder_width'])) {
        $prompt .= "- 肩宽：" . $userData['shoulder_width'] . " cm\n";
    }
    if (isset($userData['skin_tone'])) {
        $prompt .= "- 肤色：" . $userData['skin_tone'] . "\n";
    }
    if (isset($userData['face_shape'])) {
        $prompt .= "- 脸型：" . $userData['face_shape'] . "\n";
    }
    if (isset($userData['body_shape'])) {
        $prompt .= "- 体型：" . $userData['body_shape'] . "\n";
    }
    if (isset($userData['remarks']) && !empty($userData['remarks'])) {
        $prompt .= "\n用户备注：" . $userData['remarks'] . "\n";
    }
    
    // 照片提示
    if ($photoCount > 0) {
        $prompt .= "\n我已为你提供了用户的 " . $photoCount . " 张照片，请结合照片进行分析。\n";
    }
    
    // 详细的分析要求
    $prompt .= "\n请按照以下JSON格式提供你的专业分析。分析要全面、专业、个性化且实用。请使用小红书博主风格的语言，亲切友好，充满鼓励性，多使用表达赞美和惊喜的词语（如'太适合了'、'绝绝子'、'OMG'、'宝藏单品'等），重点突出用户的优势，对于需要改进的地方要委婉表达，并立即给出解决方案：\n";
    $prompt .= "```json
{
  \"身形分析\": {
    \"体型分类\": \"用热情的语言描述用户体型的专业分类，强调优点\",
    \"身材比例\": \"用鼓励的方式分析用户身材比例，突出优势\",
    \"优势特点\": [\"列出3-4个身材优势，用赞美的语气描述\"],
    \"需要注意的部位\": [\"委婉提出1-2个可以通过穿搭提升的部位，立即跟上解决方案\"]
  },
  \"肤色分析\": {
    \"色调\": \"热情描述用户肤色的色调，强调其独特美感\",
    \"适合色系\": [\"列出4-5个最适合的颜色，使用'绝配'、'超显气质'等词语\"],
    \"避免色系\": [\"委婉提出2-3个不太适合的颜色，解释原因并给出替代方案\"]
  },
  \"面部特征\": {
    \"脸型\": \"赞美性地描述用户的脸型特点\",
    \"适合发型\": [\"推荐2-3种适合的发型，使用'超级适合'、'完美衬托'等表达\"],
    \"适合妆容\": \"热情推荐适合的妆容风格，突出能提升的优点\"
  },
  \"风格定位\": {
    \"适合风格\": [\"列出3-4个最适合用户的穿衣风格，用'太适合了'、'气质拉满'等表达\"],
    \"不适合风格\": [\"委婉提出1-2个不太匹配的风格，立即给出更好的替代选择\"],
    \"个性化建议\": \"热情洋溢地给出基于用户整体形象的个性化风格建议，鼓励尝试\"
  },
  \"穿搭建议\": {
    \"上装\": [\"2-3条适合的上装建议，包括款式、领型、长度等，使用'宝藏单品'、'气质神器'等词语\"],
    \"下装\": [\"2-3条适合的下装建议，包括款式、长度等，强调如何突出优势\"],
    \"连衣裙/连体服\": [\"如适用，提供1-2条充满热情的建议\"],
    \"外套\": [\"2-3条适合的外套建议，强调如何提升整体造型\"],
    \"鞋履\": [\"2-3条鞋履建议，突出如何搭配整体造型\"],
    \"配饰\": [\"2-3条配饰建议，描述如何点亮整体造型\"]
  },
  \"搭配技巧\": {
    \"视觉重点\": \"热情建议应该突出的身体部位，使用'超级适合'、'优势满满'等表达\",
    \"比例处理\": \"详细且友好地解释如何通过穿搭优化身材比例\",
    \"层次感\": \"鼓励性地建议如何通过层次搭配提升造型感\",
    \"实用技巧\": [\"3-4条实用且具体的搭配技巧，使用'小心机'、'显瘦秘诀'等表达\"]
  },
  \"场合建议\": {
    \"日常休闲\": \"热情洋溢地推荐日常穿搭方案\",
    \"职场商务\": \"专业且鼓励地建议职场穿搭\",
    \"社交场合\": \"充满信心地推荐社交场合穿搭\",
    \"特殊场合\": \"热情推荐特殊场合的亮点穿搭\"
  },
  \"形象提升计划\": {
    \"短期建议\": [\"3-4条具体、可立即实施的建议，使用'立刻提升气质'、'马上变美'等表达\"],
    \"中长期建议\": [\"2-3条需要时间实施的建议，充满鼓励和期待\"],
    \"购物清单\": [\"列出5-6件值得投资的单品，使用'必入'、'百搭神器'等表达\"]
  },
  \"总结\": \"用热情洋溢、充满鼓励的语言总结用户的形象特点和优势，表达对用户形象提升的期待和信心。\"
}
```

请确保你的分析真诚、个性化且实用，同时保持小红书博主般的热情和鼓励。不要过度使用网络流行语，但要保持亲切友好的语气。对于用户的不足之处，要委婉表达并立即给出解决方案，重点始终放在优势和提升上。";

    return $prompt;
}

/**
 * 从文本中提取JSON数据
 *
 * @param string $text 文本内容
 * @return array 解析后的JSON数据
 */
function extractJsonFromText($text) {
    // 尝试直接解析整个文本
    $result = json_decode($text, true);
    if ($result !== null) {
        return $result;
    }

    // 尝试提取JSON部分
    if (preg_match('/```json(.*?)```/s', $text, $matches)) {
        $jsonStr = $matches[1];
        $result = json_decode($jsonStr, true);
        if ($result !== null) {
            return $result;
        }
    }

    // 再次尝试匹配没有markdown标记的JSON
    if (preg_match('/\{.*\}/s', $text, $matches)) {
        $jsonStr = $matches[0];
        $result = json_decode($jsonStr, true);
        if ($result !== null) {
            return $result;
        }
    }

    // 如果无法解析，返回原始文本作为结果
    return ['raw_text' => $text];
}

/**
 * 从base64数据检测图片的MIME类型
 *
 * @param string $base64Data base64编码的图片数据
 * @return string MIME类型
 */
function detectImageMimeTypeFromBase64($base64Data) {
    // 解码base64数据获取二进制内容
    $binaryData = base64_decode($base64Data);

    if ($binaryData === false) {
        return 'image/jpeg'; // 默认返回JPEG
    }

    // 检查文件头（magic bytes）来确定图片格式
    $header = substr($binaryData, 0, 12);

    // PNG文件头: 89 50 4E 47 0D 0A 1A 0A
    if (substr($header, 0, 8) === "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A") {
        return 'image/png';
    }

    // JPEG文件头: FF D8 FF
    if (substr($header, 0, 3) === "\xFF\xD8\xFF") {
        return 'image/jpeg';
    }

    // GIF文件头: 47 49 46 38 (GIF8)
    if (substr($header, 0, 4) === "\x47\x49\x46\x38") {
        return 'image/gif';
    }

    // WebP文件头: 52 49 46 46 ... 57 45 42 50 (RIFF...WEBP)
    if (substr($header, 0, 4) === "\x52\x49\x46\x46" && substr($header, 8, 4) === "\x57\x45\x42\x50") {
        return 'image/webp';
    }

    // BMP文件头: 42 4D (BM)
    if (substr($header, 0, 2) === "\x42\x4D") {
        return 'image/bmp';
    }

    // 如果无法识别，默认返回JPEG
    return 'image/jpeg';
}

/**
 * 增强的图片下载函数，支持重试和更好的错误处理
 *
 * @param string $imageUrl 图片URL
 * @param string $logFile 日志文件路径
 * @param int $maxRetries 最大重试次数
 * @return string|false 成功时返回图片二进制内容，失败时返回false
 */
function downloadImageWithRetry($imageUrl, $logFile, $maxRetries = 3) {
    // 验证URL格式
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "无效的图片URL格式: " . $imageUrl . PHP_EOL, FILE_APPEND);
        return false;
    }

    $attempt = 0;
    while ($attempt < $maxRetries) {
        $attempt++;
        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "第 {$attempt} 次尝试下载图片: " . $imageUrl . PHP_EOL, FILE_APPEND);

        // 初始化curl
        $ch = curl_init($imageUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);

        // 添加完整的HTTP头
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept: image/webp,image/apng,image/jpeg,image/png,image/*,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Cache-Control: no-cache',
            'Pragma: no-cache',
            'Referer: https://cyyg.alidog.cn/',
            'Sec-Fetch-Dest: image',
            'Sec-Fetch-Mode: no-cors',
            'Sec-Fetch-Site: cross-site'
        ]);

        // 执行请求
        $imageContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);
        curl_close($ch);

        // 记录响应信息
        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "响应详情 - HTTP状态: {$httpCode}, Content-Type: {$contentType}, cURL错误: {$curlError} (errno: {$curlErrno})" . PHP_EOL, FILE_APPEND);

        // 检查是否成功
        if ($imageContent === false) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "cURL执行失败: " . $curlError . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries) {
                sleep(2);
                continue;
            }
            return false;
        }

        if ($httpCode !== 200) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "HTTP状态码错误: {$httpCode}" . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries && in_array($httpCode, [500, 502, 503, 504, 408, 429])) {
                sleep(2);
                continue;
            }
            return false;
        }

        // 验证内容
        if (empty($imageContent) || strlen($imageContent) < 100) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "下载的内容无效，大小: " . strlen($imageContent) . " bytes" . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries) {
                sleep(2);
                continue;
            }
            return false;
        }

        // 验证是否为图片
        $imageInfo = @getimagesizefromstring($imageContent);
        if ($imageInfo === false) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "下载的内容不是有效的图片格式" . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries) {
                sleep(2);
                continue;
            }
            return false;
        }

        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "图片下载成功，尺寸: {$imageInfo[0]}x{$imageInfo[1]}, MIME: {$imageInfo['mime']}, 大小: " . strlen($imageContent) . " bytes" . PHP_EOL, FILE_APPEND);
        return $imageContent;
    }

    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "经过 {$maxRetries} 次尝试后仍然无法下载图片: " . $imageUrl . PHP_EOL, FILE_APPEND);
    return false;
}