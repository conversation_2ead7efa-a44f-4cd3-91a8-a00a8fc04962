<?php
/**
 * 测试个人形象分析图片格式修复
 * 用于验证JPG和PNG图片是否都能正常处理
 */

// 引入必要的函数
require_once '接口文档/个人形象分析中转api/gerenxx.php';

// 测试MIME类型检测函数
function testMimeTypeDetection() {
    echo "=== 测试MIME类型检测 ===\n";
    
    // 创建测试用的base64数据（PNG文件头）
    $pngHeader = "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A" . str_repeat("\x00", 100);
    $pngBase64 = base64_encode($pngHeader);
    
    // 创建测试用的base64数据（JPEG文件头）
    $jpegHeader = "\xFF\xD8\xFF\xE0" . str_repeat("\x00", 100);
    $jpegBase64 = base64_encode($jpegHeader);
    
    // 测试PNG检测
    $pngMime = detectImageMimeTypeFromBase64($pngBase64);
    echo "PNG检测结果: " . $pngMime . " (期望: image/png)\n";
    
    // 测试JPEG检测
    $jpegMime = detectImageMimeTypeFromBase64($jpegBase64);
    echo "JPEG检测结果: " . $jpegMime . " (期望: image/jpeg)\n";
    
    // 测试无效数据
    $invalidMime = detectImageMimeTypeFromBase64("invalid_base64_data");
    echo "无效数据检测结果: " . $invalidMime . " (期望: image/jpeg)\n";
    
    echo "\n";
}

// 测试图片下载函数
function testImageDownload() {
    echo "=== 测试图片下载功能 ===\n";
    
    $logFile = __DIR__ . '/test_download.log';
    
    // 测试有效的图片URL（使用一个公开的测试图片）
    $testUrls = [
        'https://via.placeholder.com/150.png',  // PNG格式
        'https://via.placeholder.com/150.jpg',  // JPG格式
        'https://httpbin.org/image/png',         // PNG格式
        'https://httpbin.org/image/jpeg'         // JPEG格式
    ];
    
    foreach ($testUrls as $url) {
        echo "测试下载: " . $url . "\n";
        $result = downloadImageWithRetry($url, $logFile, 2);
        
        if ($result !== false) {
            echo "  下载成功，大小: " . strlen($result) . " bytes\n";
            
            // 检测MIME类型
            $base64 = base64_encode($result);
            $mimeType = detectImageMimeTypeFromBase64($base64);
            echo "  检测到的MIME类型: " . $mimeType . "\n";
        } else {
            echo "  下载失败\n";
        }
        echo "\n";
    }
    
    // 测试无效URL
    echo "测试无效URL:\n";
    $invalidResult = downloadImageWithRetry('invalid_url', $logFile, 1);
    echo "无效URL结果: " . ($invalidResult === false ? '失败（正确）' : '意外成功') . "\n";
    
    echo "\n";
}

// 模拟完整的个人形象分析请求
function testFullAnalysisFlow() {
    echo "=== 测试完整分析流程 ===\n";
    
    // 模拟请求数据
    $testData = [
        'userData' => [
            'gender' => 2,
            'height' => 165,
            'weight' => 55,
            'bust' => 85,
            'waist' => 65,
            'hips' => 90,
            'shoulder_width' => 38,
            'skin_tone' => '偏白',
            'face_shape' => '瓜子脸',
            'body_shape' => '梨形',
            'remarks' => '测试用户'
        ],
        'photoUrls' => [
            'https://via.placeholder.com/300.png',
            'https://via.placeholder.com/300.jpg'
        ],
        'photoBase64' => [],
        'analysisId' => 'test_' . time()
    ];
    
    echo "模拟数据准备完成\n";
    echo "用户数据: " . json_encode($testData['userData'], JSON_UNESCAPED_UNICODE) . "\n";
    echo "图片URL数量: " . count($testData['photoUrls']) . "\n";
    
    // 这里只测试数据处理部分，不实际调用Gemini API
    echo "测试完成（未实际调用API）\n\n";
}

// 运行所有测试
function runAllTests() {
    echo "开始测试个人形象分析图片格式修复\n";
    echo "时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    testMimeTypeDetection();
    testImageDownload();
    testFullAnalysisFlow();
    
    echo "所有测试完成\n";
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    runAllTests();
}
?>
