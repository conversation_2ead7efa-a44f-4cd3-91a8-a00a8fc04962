<?php
/**
 * 个人形象分析请求API
 * 接收用户数据，将数据转发到Gemini中转API，并保存分析结果
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => 'Method Not Allowed']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Authorization header is required']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit;
}

// 获取用户ID
$userId = $payload['sub'];

// 记录日志
$logFile = __DIR__ . '/logs/image_analysis_requests.log';
$logDir = dirname($logFile);
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

// 获取请求数据
$rawData = file_get_contents('php://input');
$requestData = json_decode($rawData, true);

// 记录请求
file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "用户ID: $userId, 请求数据: " . json_encode($requestData, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);

// 检查必要参数
if (!isset($requestData['analysisId']) || empty($requestData['analysisId'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少分析ID']);
    exit;
}

$analysisId = $requestData['analysisId'];

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 检查分析记录是否存在并且已支付
$stmt = $conn->prepare("
    SELECT * FROM user_image_analysis 
    WHERE id = :analysis_id AND user_id = :user_id AND payment_status = 'paid'
");
$stmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
$stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
$stmt->execute();
$analysis = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$analysis) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '未找到已支付的分析记录']);
    exit;
}

// 检查记录状态
if ($analysis['status'] != 'pending') {
    // 如果已经处理过，直接返回当前状态
    echo json_encode([
        'error' => false,
        'msg' => '分析请求已提交',
        'data' => [
            'analysis_id' => $analysisId,
            'status' => $analysis['status']
        ]
    ]);
    exit;
}

// 更新状态为处理中
$updateStmt = $conn->prepare("
    UPDATE user_image_analysis 
    SET status = 'processing', updated_at = NOW() 
    WHERE id = :analysis_id
");
$updateStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
$updateStmt->execute();

// 准备发送到Gemini中转API的数据
$userData = [
    'gender' => $analysis['gender'],
    'height' => $analysis['height'],
    'weight' => $analysis['weight'],
    'bust' => $analysis['bust'],
    'waist' => $analysis['waist'],
    'hips' => $analysis['hips'],
    'shoulder_width' => $analysis['shoulder_width'],
    'skin_tone' => $analysis['skin_tone'],
    'face_shape' => $analysis['face_shape'],
    'body_shape' => $analysis['body_shape'],
    'remarks' => $analysis['remarks']
];

// 处理照片URL
$photoUrls = [];
$photoBase64 = [];
if (!empty($analysis['photo_urls'])) {
    $photoUrls = json_decode($analysis['photo_urls'], true);
    if (!is_array($photoUrls)) {
        $photoUrls = [];
    }
    
    // 下载图片并转换为base64
    foreach ($photoUrls as $photoUrl) {
        $base64Image = getImageAsBase64($photoUrl, $logFile, 3); // 最多重试3次
        if ($base64Image) {
            $photoBase64[] = $base64Image;
        }
    }
    
    // 记录转换结果
    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "转换图片完成: 共 " . count($photoUrls) . " 张图片，成功转换 " . count($photoBase64) . " 张\n", FILE_APPEND);
}

// 准备发送的完整数据
$postData = [
    'userData' => $userData,
    'photoUrls' => $photoUrls, // 保留原URL以便日志记录
    'photoBase64' => $photoBase64, // 添加base64编码的图片
    'analysisId' => $analysisId
];

// 发送请求到Gemini中转API
$geminiApiUrl = 'https://www.furrywoo.com/gemini/gerenxx.php';
$ch = curl_init($geminiApiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

// 记录API响应
file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "Gemini API响应 (HTTP $httpCode): " . $response . PHP_EOL, FILE_APPEND);

// 检查curl错误
if ($curlError) {
    // 更新状态为失败
    $updateStmt = $conn->prepare("
        UPDATE user_image_analysis 
        SET status = 'failed', updated_at = NOW() 
        WHERE id = :analysis_id
    ");
    $updateStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $updateStmt->execute();
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => 'API请求失败: ' . $curlError]);
    exit;
}

// 检查HTTP状态码
if ($httpCode !== 200) {
    // 更新状态为失败
    $updateStmt = $conn->prepare("
        UPDATE user_image_analysis 
        SET status = 'failed', updated_at = NOW() 
        WHERE id = :analysis_id
    ");
    $updateStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $updateStmt->execute();
    
    http_response_code($httpCode);
    echo $response;
    exit;
}

// 解析响应
$geminiResponse = json_decode($response, true);

// 检查是否有错误
if (isset($geminiResponse['error']) && $geminiResponse['error']) {
    // 更新状态为失败
    $updateStmt = $conn->prepare("
        UPDATE user_image_analysis 
        SET status = 'failed', updated_at = NOW() 
        WHERE id = :analysis_id
    ");
    $updateStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $updateStmt->execute();
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '分析失败: ' . ($geminiResponse['msg'] ?? '未知错误')]);
    exit;
}

// 获取分析结果
$analysisResult = $geminiResponse['data'] ?? [];
$rawText = $geminiResponse['rawText'] ?? '';

// 将分析结果保存到数据库
$updateStmt = $conn->prepare("
    UPDATE user_image_analysis 
    SET 
        analysis_result = :analysis_result,
        status = 'completed',
        analysis_time = NOW(),
        updated_at = NOW()
    WHERE id = :analysis_id
");
$analysisResultJson = json_encode($analysisResult, JSON_UNESCAPED_UNICODE);
$updateStmt->bindParam(':analysis_result', $analysisResultJson, PDO::PARAM_STR);
$updateStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
$result = $updateStmt->execute();

if (!$result) {
    // 更新失败
    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "保存分析结果失败: " . json_encode($conn->errorInfo(), JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '保存分析结果失败']);
    exit;
}

// 返回成功响应
echo json_encode([
    'error' => false,
    'msg' => '分析完成',
    'data' => [
        'analysis_id' => $analysisId,
        'status' => 'completed'
    ]
]);

/**
 * 下载图片并转换为base64编码
 *
 * @param string $imageUrl 图片URL
 * @param string $logFile 日志文件路径
 * @param int $maxRetries 最大重试次数
 * @return string|false 成功时返回base64编码的图片，失败时返回false
 */
function getImageAsBase64($imageUrl, $logFile, $maxRetries = 3) {
    // 记录下载尝试
    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "尝试下载图片: " . $imageUrl . PHP_EOL, FILE_APPEND);

    // 验证URL格式
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "无效的图片URL格式: " . $imageUrl . PHP_EOL, FILE_APPEND);
        return false;
    }

    // 检查URL是否为图片格式
    $urlPath = parse_url($imageUrl, PHP_URL_PATH);
    $extension = strtolower(pathinfo($urlPath, PATHINFO_EXTENSION));
    $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];

    if (!in_array($extension, $validExtensions)) {
        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "URL不是有效的图片格式，扩展名: " . $extension . PHP_EOL, FILE_APPEND);
    }

    $attempt = 0;
    while ($attempt < $maxRetries) {
        $attempt++;
        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "第 {$attempt} 次尝试下载图片" . PHP_EOL, FILE_APPEND);

        // 初始化curl
        $ch = curl_init($imageUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // 最大重定向次数

        // 添加更完整的HTTP头
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept: image/webp,image/apng,image/jpeg,image/png,image/*,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Cache-Control: no-cache',
            'Pragma: no-cache',
            'Referer: https://cyyg.alidog.cn/',
            'Sec-Fetch-Dest: image',
            'Sec-Fetch-Mode: no-cors',
            'Sec-Fetch-Site: cross-site'
        ]);

        // 执行请求
        $imageContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $contentLength = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);
        curl_close($ch);

        // 记录详细的响应信息
        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "响应详情 - HTTP状态: {$httpCode}, Content-Type: {$contentType}, Content-Length: {$contentLength}, cURL错误: {$curlError} (errno: {$curlErrno})" . PHP_EOL, FILE_APPEND);

        // 检查是否成功获取图片
        if ($imageContent === false) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "cURL执行失败: " . $curlError . " (errno: {$curlErrno})" . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries) {
                sleep(2); // 等待2秒后重试
                continue;
            }
            return false;
        }

        if ($httpCode !== 200) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "HTTP状态码错误: {$httpCode}" . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries && in_array($httpCode, [500, 502, 503, 504, 408, 429])) {
                sleep(2); // 对于服务器错误，等待后重试
                continue;
            }
            return false;
        }

        // 验证内容是否为图片
        if (empty($imageContent)) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "下载的内容为空" . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries) {
                sleep(2);
                continue;
            }
            return false;
        }

        // 检查内容长度
        if (strlen($imageContent) < 100) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "下载的内容太小，可能不是有效图片，大小: " . strlen($imageContent) . " bytes" . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries) {
                sleep(2);
                continue;
            }
            return false;
        }

        // 验证图片格式
        $imageInfo = @getimagesizefromstring($imageContent);
        if ($imageInfo === false) {
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "下载的内容不是有效的图片格式" . PHP_EOL, FILE_APPEND);
            if ($attempt < $maxRetries) {
                sleep(2);
                continue;
            }
            return false;
        }

        // 转换为base64
        $base64Image = base64_encode($imageContent);

        // 记录成功
        $truncatedBase64 = substr($base64Image, 0, 30) . '...' . substr($base64Image, -30);
        file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "图片下载并转换成功，尺寸: {$imageInfo[0]}x{$imageInfo[1]}, MIME: {$imageInfo['mime']}, base64长度: " . strlen($base64Image) . " 预览: " . $truncatedBase64 . PHP_EOL, FILE_APPEND);

        return $base64Image;
    }

    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "经过 {$maxRetries} 次尝试后仍然无法下载图片: " . $imageUrl . PHP_EOL, FILE_APPEND);
    return false;
}