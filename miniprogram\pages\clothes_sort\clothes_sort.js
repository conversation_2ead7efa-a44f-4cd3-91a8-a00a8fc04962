const app = getApp();

Page({
  data: {
    clothesList: [],
    loading: true,
    saving: false,
    originalOrder: [], // 保存原始顺序，用于比较是否有变化

    // 拖拽相关数据
    draggingIndex: -1, // 当前拖拽的项目索引
    isDragging: false, // 是否正在拖拽
    itemHeight: 168, // 每个项目的高度（rpx）
    longPressTimer: null, // 长按定时器
    startY: 0, // 触摸开始时的Y坐标
    dragOffsetY: 0, // 拖拽偏移量
    dragStartIndex: -1, // 拖拽开始时的索引
    lastUpdateTime: 0, // 上次更新时间，用于节流
    scrollTop: 0 // 页面滚动位置
  },

  onLoad() {
    console.log('衣物排序页面开始加载');
    try {
      this.loadClothes();
    } catch (error) {
      console.error('页面加载出错:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
      wx.navigateBack();
    }
  },

  // 加载衣物数据
  loadClothes() {
    console.log('开始加载衣物数据');
    this.setData({ loading: true });

    // 优先使用globalData中的token，如果没有再从本地存储获取
    let token = app.globalData.token;
    if (!token) {
      token = wx.getStorageSync('token');
    }

    console.log('app.globalData.token:', app.globalData.token ? '存在' : '不存在');
    console.log('本地存储token:', wx.getStorageSync('token') ? '存在' : '不存在');
    console.log('最终使用的token:', token ? '存在' : '不存在');
    console.log('用户登录状态:', app.globalData.isLoggedIn);
    console.log('是否体验账号:', app.globalData.useMockUser);

    if (!token) {
      console.log('token不存在，返回登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    const apiUrl = `${app.globalData.apiBaseUrl}/get_clothes.php`;
    console.log('请求URL:', apiUrl);
    console.log('请求参数:', { all_clothes: 1 });

    wx.request({
      url: apiUrl,
      method: 'GET',
      header: {
        'Authorization': token
      },
      data: {
        all_clothes: 1 // 获取所有衣物
      },
      success: (res) => {
        console.log('网络请求成功，响应数据:', res);

        if (res.data && !res.data.error) {
          console.log('获取到衣物数据，数量:', res.data.data ? res.data.data.length : 0);

          // 转换字段名称 - 后端使用下划线命名，前端使用驼峰命名
          const clothes = res.data.data.map(item => {
            return {
              id: item.id,
              name: item.name,
              category: item.category,
              imageUrl: item.image_url,
              tags: item.tags,
              description: item.description,
              createdAt: item.created_at,
              sortOrder: item.sort_order || item.id
            };
          });

          console.log('转换后的衣物数据:', clothes);

          // 按sort_order排序
          const sortedClothes = clothes.sort((a, b) => {
            return a.sortOrder - b.sortOrder;
          });

          console.log('排序后的衣物数据:', sortedClothes);

          this.setData({
            clothesList: sortedClothes,
            originalOrder: sortedClothes.map(item => item.id),
            loading: false
          });

          console.log('数据设置完成');
        } else {
          wx.showToast({
            title: res.data?.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 向上移动
  moveUp(e) {
    const index = e.currentTarget.dataset.index;
    if (index === 0) return;

    const clothesList = [...this.data.clothesList];
    const item = clothesList[index];
    clothesList.splice(index, 1);
    clothesList.splice(index - 1, 0, item);

    this.setData({ clothesList });
  },

  // 向下移动
  moveDown(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.clothesList.length - 1) return;

    const clothesList = [...this.data.clothesList];
    const item = clothesList[index];
    clothesList.splice(index, 1);
    clothesList.splice(index + 1, 0, item);

    this.setData({ clothesList });
  },

  // 节流函数
  throttle(func, delay) {
    const now = Date.now();
    if (now - this.data.lastUpdateTime >= delay) {
      this.setData({ lastUpdateTime: now });
      return func();
    }
  },

  // 获取页面滚动位置
  getScrollTop() {
    return new Promise((resolve) => {
      const query = wx.createSelectorQuery();
      query.selectViewport().scrollOffset();
      query.exec((res) => {
        if (res[0]) {
          this.setData({ scrollTop: res[0].scrollTop });
          resolve(res[0].scrollTop);
        } else {
          resolve(0);
        }
      });
    });
  },

  // 触摸开始事件
  onTouchStart(e) {
    const index = e.currentTarget.dataset.index;
    const touch = e.touches[0];

    // 获取页面滚动位置
    this.getScrollTop();

    // 记录开始位置
    this.setData({
      startY: touch.pageY,
      dragStartIndex: index
    });

    // 设置长按定时器（减少延迟时间）
    this.data.longPressTimer = setTimeout(() => {
      this.setData({
        draggingIndex: index,
        isDragging: true,
        dragOffsetY: 0
      });

      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
    }, 300); // 300ms后开始拖拽
  },

  // 触摸移动事件
  onTouchMove(e) {
    // 阻止页面滚动
    e.preventDefault && e.preventDefault();
    e.stopPropagation && e.stopPropagation();

    if (!this.data.isDragging) return;

    const touch = e.touches[0];
    const offsetY = touch.pageY - this.data.startY;

    // 使用节流优化视觉效果性能
    this.throttle(() => {
      this.setData({
        dragOffsetY: offsetY
      });
    }, 16); // 约60fps

    // 计算目标位置 - 修正rpx到px的转换
    // 在大多数设备上，1rpx ≈ 0.5px，但这里使用更精确的计算
    const systemInfo = wx.getSystemInfoSync();
    const rpxToPx = systemInfo.windowWidth / 750; // 750是设计稿宽度
    const itemHeightPx = this.data.itemHeight * rpxToPx;

    const targetIndex = Math.round(offsetY / itemHeightPx) + this.data.dragStartIndex;
    const validTargetIndex = Math.max(0, Math.min(targetIndex, this.data.clothesList.length - 1));

    // 如果位置发生变化，立即重新排序
    if (validTargetIndex !== this.data.draggingIndex) {
      const clothesList = [...this.data.clothesList];
      const draggedItem = clothesList[this.data.draggingIndex];

      // 移除拖拽项
      clothesList.splice(this.data.draggingIndex, 1);

      // 插入到新位置
      clothesList.splice(validTargetIndex, 0, draggedItem);

      // 更新拖拽索引和列表
      this.setData({
        clothesList,
        draggingIndex: validTargetIndex
      });
    }
  },

  // 触摸结束事件
  onTouchEnd(e) {
    // 清除长按定时器
    if (this.data.longPressTimer) {
      clearTimeout(this.data.longPressTimer);
      this.data.longPressTimer = null;
    }

    // 如果正在拖拽，进行最终位置确认
    if (this.data.isDragging) {
      const touch = e.changedTouches[0];
      const offsetY = touch.pageY - this.data.startY;

      // 计算最终目标位置
      const systemInfo = wx.getSystemInfoSync();
      const rpxToPx = systemInfo.windowWidth / 750;
      const itemHeightPx = this.data.itemHeight * rpxToPx;

      const targetIndex = Math.round(offsetY / itemHeightPx) + this.data.dragStartIndex;
      const validTargetIndex = Math.max(0, Math.min(targetIndex, this.data.clothesList.length - 1));

      // 确保最终位置正确
      if (validTargetIndex !== this.data.draggingIndex) {
        const clothesList = [...this.data.clothesList];
        const draggedItem = clothesList[this.data.draggingIndex];

        // 移除拖拽项
        clothesList.splice(this.data.draggingIndex, 1);

        // 插入到新位置
        clothesList.splice(validTargetIndex, 0, draggedItem);

        // 更新列表
        this.setData({
          clothesList
        });
      }
    }

    // 重置拖拽状态
    this.setData({
      draggingIndex: -1,
      isDragging: false,
      dragOffsetY: 0,
      dragStartIndex: -1
    });
  },

  // 防止页面滚动
  preventMove() {
    // 空函数，用于阻止页面滚动
  },

  // 保存排序
  saveSortOrder() {
    const currentOrder = this.data.clothesList.map(item => item.id);
    
    // 检查是否有变化
    if (JSON.stringify(currentOrder) === JSON.stringify(this.data.originalOrder)) {
      wx.showToast({
        title: '排序未发生变化',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });

    // 优先使用globalData中的token，如果没有再从本地存储获取
    let token = app.globalData.token;
    if (!token) {
      token = wx.getStorageSync('token');
    }
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/update_clothes_sort_order.php`,
      method: 'POST',
      header: {
        'Authorization': token,
        'Content-Type': 'application/json'
      },
      data: {
        clothes_order: currentOrder
      },
      success: (res) => {
        if (res.data && !res.data.error) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });
          
          // 更新原始顺序
          this.setData({
            originalOrder: currentOrder,
            saving: false
          });
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data?.message || '保存失败',
            icon: 'none'
          });
          this.setData({ saving: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ saving: false });
      }
    });
  }
});
