.container {
  min-height: 100vh;
  background-color: #fff;
  padding-bottom: 120rpx;
}

/* 头部说明 */
.header-info {
  background-color: #fff;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 衣物列表 */
.clothes-list {
  flex: 1;
  padding: 0 0 20rpx 0;
}

/* 拖拽区域 */
.movable-area {
  width: 100%;
  position: relative;
}

.clothes-item {
  width: 100%;
  height: 168rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

/* 拖拽状态样式 */
.clothes-item.dragging {
  transform: scale(1.02);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  z-index: 999;
  border-radius: 12rpx;
  border: 2rpx solid #000;
}

.clothes-content {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  height: 100%;
  box-sizing: border-box;
  transition: background-color 0.3s;
}

.clothes-content:active {
  background-color: #f5f5f5;
}

/* 拖拽时禁用点击效果 */
.clothes-item.dragging .clothes-content:active {
  background-color: transparent;
}

/* 衣物图片 */
.clothes-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  margin-right: 24rpx;
}

/* 衣物信息 */
.clothes-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.clothes-name {
  font-size: 32rpx;
  color: #000;
  font-weight: 500;
  line-height: 1.3;
}

.clothes-category {
  font-size: 26rpx;
  color: #666;
}

/* 排序控制按钮 */
.sort-controls {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.sort-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #fff;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.sort-btn:active {
  background-color: #000;
}

.sort-btn:active .sort-icon {
  color: #fff;
}

.sort-icon {
  font-size: 32rpx;
  color: #666;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 底部保存按钮 */
.save-btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 30rpx;
  border-top: 1rpx solid #e8e8e8;
  z-index: 100;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background-color: #000;
  color: #fff;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn:disabled {
  background-color: #ccc;
  color: #999;
}

.save-btn::after {
  border: none;
}
