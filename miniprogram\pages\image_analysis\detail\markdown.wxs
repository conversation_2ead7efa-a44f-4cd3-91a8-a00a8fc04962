/**
 * Markdown文本处理模块
 */

// 处理加粗文本，将**文字**格式转换为<span class="markdown-bold">文字</span>
function formatBoldText(text) {
  if (!text) return '';
  
  // 处理HTML特殊字符，防止XSS攻击
  text = escapeHtml(text);
  
  // 使用简单的字符串替换方法
  var result = '';
  var inBold = false;
  var currentText = '';
  
  for (var i = 0; i < text.length; i++) {
    // 检查是否是 ** 序列
    if (i < text.length - 1 && text[i] === '*' && text[i + 1] === '*') {
      // 处理当前积累的文本
      if (inBold) {
        // 如果当前在加粗状态，添加加粗文本
        result += '<span class="markdown-bold">' + currentText + '</span>';
      } else {
        // 如果当前不在加粗状态，添加普通文本
        result += currentText;
      }
      
      // 切换加粗状态
      inBold = !inBold;
      
      // 重置当前文本
      currentText = '';
      
      // 跳过下一个 *
      i++;
    } else {
      // 积累当前字符
      currentText += text[i];
    }
  }
  
  // 处理剩余的文本
  // 如果结束时仍在加粗状态，说明有未闭合的 **，将其视为普通文本
  if (inBold) {
    result += '**' + currentText;
  } else {
    result += currentText;
  }
  
  return result;
}

// HTML特殊字符转义函数
function escapeHtml(text) {
  var map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  
  // 在WXS中使用getRegExp函数创建正则表达式
  var htmlRegex = getRegExp("[&<>\"']", "g");
  return text.replace(htmlRegex, function(m) { return map[m]; });
}

// 将处理后的文本拆分为段落
function splitIntoParagraphs(text) {
  if (!text) return [];
  
  // 按换行符分割文本
  var paragraphs = text.split('\n');
  
  // 过滤掉空段落，并修剪每个段落的前后空白
  return paragraphs.filter(function(p) {
    return p.trim().length > 0;
  }).map(function(p) {
    return p.trim();
  });
}

// 导出模块函数
module.exports = {
  formatBoldText: formatBoldText,
  splitIntoParagraphs: splitIntoParagraphs
};
