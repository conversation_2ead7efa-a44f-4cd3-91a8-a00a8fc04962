<!-- 引入Markdown处理模块 -->
<wxs src="./markdown.wxs" module="md" />

<view class="container">
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-icon"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <block wx:elif="{{!error}}">
    <!-- 添加分享查看提示条 -->
    <view class="share-banner" wx:if="{{isSharedView}}">
      <text class="share-text">您正在查看分享的形象分析结果</text>
    </view>
    
    <view class="status-bar {{analysis.status}}">
      <text class="status-text">{{statusText[analysis.status]}}</text>
      <text class="status-desc" wx:if="{{analysis.status === 'pending'}}">您的分析请求已提交，正在等待处理</text>
      <text class="status-desc" wx:elif="{{analysis.status === 'processing'}}">正在为您生成专业形象分析报告</text>
      <text class="status-desc" wx:elif="{{analysis.status === 'completed'}}">分析完成于 {{analysis.analysis_time}}</text>
      <text class="status-desc" wx:elif="{{analysis.status === 'failed'}}">很抱歉，分析失败。请重新提交</text>
    </view>
    
    <scroll-view scroll-y class="content">
      <view wx:if="{{analysis.status === 'completed'}}" class="analysis-result">
        <!-- 照片展示 -->
        <view class="photo-section" wx:if="{{analysis.photo_urls && analysis.photo_urls.length > 0}}">
          <view class="section-title">您的照片</view>
          <scroll-view scroll-x class="photo-scroll">
            <view class="photo-list">
              <view class="photo-item" wx:for="{{analysis.photo_urls}}" wx:key="index">
                <image src="{{item}}" mode="aspectFit" class="photo-image" bindtap="previewImage" data-url="{{item}}"></image>
              </view>
            </view>
          </scroll-view>
        </view>
        
        <!-- 身形分析 -->
        <view class="section" wx:if="{{analysis.analysis_result['身形分析']}}">
          <view class="section-title">身形分析</view>
          <view class="section-content">
            <view class="analysis-item">
              <view class="item-label">体型分类</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['身形分析']['体型分类'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['身形分析']['体型分类']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>

            <view class="analysis-item">
              <view class="item-label">身材比例</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['身形分析']['身材比例'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['身形分析']['身材比例']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">优势特点</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['身形分析']['优势特点']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">需要注意的部位</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['身形分析']['需要注意的部位']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 肤色分析 -->
        <view class="section" wx:if="{{analysis.analysis_result['肤色分析']}}">
          <view class="section-title">肤色分析</view>
          <view class="section-content">
            <view class="analysis-item">
              <view class="item-label">色调</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['肤色分析']['色调'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['肤色分析']['色调']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">适合色系</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['肤色分析']['适合色系']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">避免色系</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['肤色分析']['避免色系']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 面部特征 -->
        <view class="section" wx:if="{{analysis.analysis_result['面部特征']}}">
          <view class="section-title">面部特征</view>
          <view class="section-content">
            <view class="analysis-item">
              <view class="item-label">脸型</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['面部特征']['脸型'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['面部特征']['脸型']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">适合发型</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['面部特征']['适合发型']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">适合妆容</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['面部特征']['适合妆容'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['面部特征']['适合妆容']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 风格定位 -->
        <view class="section" wx:if="{{analysis.analysis_result['风格定位']}}">
          <view class="section-title">风格定位</view>
          <view class="section-content">
            <view class="analysis-item">
              <view class="item-label">适合风格</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['风格定位']['适合风格']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">不适合风格</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['风格定位']['不适合风格']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">个性化建议</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['风格定位']['个性化建议'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['风格定位']['个性化建议']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 穿搭建议 -->
        <view class="section" wx:if="{{analysis.analysis_result['穿搭建议']}}">
          <view class="section-title">穿搭建议</view>
          <view class="section-content">
            <block wx:for="{{analysis.gender === 1 ? ['上装', '下装', '外套', '鞋履', '配饰'] : ['上装', '下装', '连衣裙/连体服', '外套', '鞋履', '配饰']}}" wx:key="index" wx:for-item="category">
              <view class="analysis-item" wx:if="{{analysis.analysis_result['穿搭建议'][category]}}">
                <view class="item-label">{{category}}</view>
                <view class="item-list">
                  <view class="list-item" wx:for="{{analysis.analysis_result['穿搭建议'][category]}}" wx:key="index">{{item}}</view>
                </view>
              </view>
            </block>
          </view>
        </view>
        
        <!-- 搭配技巧 -->
        <view class="section" wx:if="{{analysis.analysis_result['搭配技巧']}}">
          <view class="section-title">搭配技巧</view>
          <view class="section-content">
            <view class="analysis-item">
              <view class="item-label">视觉重点</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['搭配技巧']['视觉重点'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['搭配技巧']['视觉重点']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>

            <view class="analysis-item">
              <view class="item-label">比例处理</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['搭配技巧']['比例处理'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['搭配技巧']['比例处理']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>

            <view class="analysis-item">
              <view class="item-label">层次感</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['搭配技巧']['层次感'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['搭配技巧']['层次感']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">实用技巧</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['搭配技巧']['实用技巧']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 场合建议 -->
        <view class="section" wx:if="{{analysis.analysis_result['场合建议']}}">
          <view class="section-title">场合建议</view>
          <view class="section-content">
            <block wx:for="{{['日常休闲', '职场商务', '社交场合', '特殊场合']}}" wx:key="index" wx:for-item="category">
              <view class="analysis-item" wx:if="{{analysis.analysis_result['场合建议'][category]}}">
                <view class="item-label">{{category}}</view>
                <view class="item-value">
                  <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['场合建议'][category])}}" wx:key="index">
                    <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                    <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['场合建议'][category]).length - 1}}" style="height: 8rpx;"></view>
                  </block>
                </view>
              </view>
            </block>
          </view>
        </view>
        
        <!-- 形象提升计划 -->
        <view class="section" wx:if="{{analysis.analysis_result['形象提升计划']}}">
          <view class="section-title">形象提升计划</view>
          <view class="section-content">
            <view class="analysis-item">
              <view class="item-label">短期建议</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['形象提升计划']['短期建议']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">中长期建议</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['形象提升计划']['中长期建议']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
            
            <view class="analysis-item">
              <view class="item-label">购物清单</view>
              <view class="item-list">
                <view class="list-item" wx:for="{{analysis.analysis_result['形象提升计划']['购物清单']}}" wx:key="index">{{item}}</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 总结 -->
        <view class="section" wx:if="{{analysis.analysis_result['总结']}}">
          <view class="section-title">总结</view>
          <view class="section-content">
            <view class="analysis-item">
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysis.analysis_result['总结'])}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysis.analysis_result['总结']).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view wx:else class="waiting-content">
        <view wx:if="{{analysis.status === 'pending'}}" class="waiting-message">
          <view class="waiting-icon">⏳</view>
          <view class="waiting-text">您的分析请求已提交，正在等待处理</view>
          <view class="waiting-subtext">分析将在支付成功后自动开始</view>
          <view class="button-group" wx:if="{{analysis.payment_status !== 'paid' && !isSharedView}}">
            <button class="pay-btn" bindtap="goToPayment">立即支付</button>
          </view>
        </view>
        
        <view wx:elif="{{analysis.status === 'processing'}}" class="waiting-message">
          <view class="waiting-icon">��</view>
          <view class="waiting-text">正在为您生成专业形象分析报告</view>
          <view class="waiting-subtext">分析完成后将自动显示结果，请耐心等待</view>
        </view>
        
        <view wx:elif="{{analysis.status === 'failed'}}" class="waiting-message">
          <view class="waiting-icon">❌</view>
          <view class="waiting-text">很抱歉，分析失败</view>
          <view class="waiting-subtext">请重新提交分析请求</view>
          <view class="button-group" wx:if="{{!isSharedView}}">
            <button class="retry-btn" bindtap="goToAnalysis">重新分析</button>
            <button wx:if="{{analysis.payment_status !== 'paid'}}" class="pay-btn" bindtap="goToPayment">立即支付</button>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 非分享视图才显示操作按钮 -->
    <view class="footer" wx:if="{{analysis.status === 'pending' && !isSharedView}}">
      <button class="action-btn" bindtap="{{analysis.payment_status === 'paid' ? 'requestAnalysis' : 'goToPayment'}}" disabled="{{false}}">
        {{analysis.payment_status === 'paid' ? '开始分析' : '立即支付'}}
      </button>
    </view>
    
    <view class="footer" wx:elif="{{analysis.status === 'processing' && !isSharedView}}">
      <button class="action-btn refresh-btn" bindtap="refreshAnalysis">刷新状态</button>
    </view>

    <view class="footer" wx:elif="{{analysis.status === 'completed'}}">
      <view class="button-row">
      <button class="action-btn share-btn" open-type="share">分享分析结果</button>
        <button class="action-btn recommend-btn" bindtap="goToRecommendation" wx:if="{{!isSharedView}}">基于此分析推荐穿搭</button>
      </view>
    </view>
  </block>
  
  <view wx:else class="error-container">
    <view class="error-icon">😢</view>
    <view class="error-text">{{errorMsg}}</view>
    <view class="button-group">
      <button class="retry-btn" bindtap="goToHistory">返回历史记录</button>
       <!--  <button class="pay-btn" bindtap="goToPayment">立即支付</button>-->
    </view>
  </view>
</view> 